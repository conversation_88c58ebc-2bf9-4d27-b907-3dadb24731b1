"""
Wildlife Anomaly Detection API
FastAPI application for YOLOv5-based wildlife detection without modifying YOLOv5 source.
"""

from fastapi import FastAPI, UploadFile, File, Form, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from fastapi.middleware.cors import CORSMiddleware
import os
import shutil
import uvicorn

# Import our custom modules (these handle YOLOv5 compatibility)
from ..utils.location_generator import add_random_location
from ..utils.load_data import load_data
from ..core.video_processor import predict_video
from ..utils.notification import sendAlert

# Initialize FastAPI app
app = FastAPI(
    title="Wildlife Anomaly Detection API",
    description="YOLOv5-based wildlife detection and anomaly monitoring system",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# Add CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Setup templates
templates = Jinja2Templates(directory="./")

# Mount static file directories
app.mount("/img", StaticFiles(directory="static/img"), name="img")
app.mount("/css", StaticFiles(directory="static/css"), name="css")
app.mount("/lib", StaticFiles(directory="static/lib"), name="lib")
app.mount("/js", StaticFiles(directory="static/js"), name="js")
app.mount("/temp", StaticFiles(directory="static/temp"), name="temp")
app.mount("/out", StaticFiles(directory="static/out"), name="out")

# Configuration
GIVEN_LAT = 22.606803
GIVEN_LON = 85.338173
MAX_DISTANCE_KM = 20


@app.on_event("startup")
async def startup_event():
    """Initialize the application on startup."""
    print("🚀 Starting Wildlife Anomaly Detection API...")

    # Ensure required directories exist
    os.makedirs("static/temp", exist_ok=True)
    os.makedirs("static/out", exist_ok=True)

    # Pre-load models to reduce first request latency
    try:
        from ..core.model_loader import model_loader

        print("📥 Pre-loading YOLOv5 models...")
        model_loader.load_models()
        print("✅ Models pre-loaded successfully!")
    except Exception as e:
        print(f"⚠️ Warning: Could not pre-load models: {e}")
        print("Models will be loaded on first request.")


@app.get("/", response_class=HTMLResponse)
async def get_homepage():
    """Serve the main homepage."""
    try:
        request = {"title": "index"}
        return templates.TemplateResponse("static/index.html", {"request": request})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error serving homepage: {str(e)}")


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "Wildlife Anomaly Detection API",
        "version": "1.0.0",
    }


@app.get("/{page_name}")
async def get_page(page_name: str):
    """Serve static HTML pages."""
    try:
        # Remove file extension if present
        page_name = page_name.split(".")[0]

        # Check if the HTML file exists
        html_file = f"static/{page_name}.html"
        if not os.path.exists(html_file):
            raise HTTPException(status_code=404, detail=f"Page not found: {page_name}")

        request = {"title": page_name}
        return templates.TemplateResponse(html_file, {"request": request})
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error serving page: {str(e)}")


@app.post("/upload/")
async def predict_video_endpoint(
    video: UploadFile = File(...),
    selected_class: str = Form(...),
    getAlert: bool = Form(False),
):
    """
    Main video upload and prediction endpoint.

    Args:
        video: Video file to process
        selected_class: Model type ('Daylight' or 'Thermal')
        getAlert: Whether to send SMS alerts for detections

    Returns:
        JSON response with detection results and video paths
    """
    try:
        print(f"📤 Received video upload: {video.filename}")
        print(f"🔧 Model type: {selected_class}")
        print(f"🚨 Alerts enabled: {getAlert}")

        # Validate inputs
        if not video.filename:
            raise HTTPException(status_code=400, detail="No video file provided")

        if selected_class not in ["Daylight", "Thermal"]:
            raise HTTPException(
                status_code=400,
                detail="Invalid model type. Use 'Daylight' or 'Thermal'",
            )

        # Save uploaded video
        temp_video_path = f"/temp/{video.filename}"
        full_temp_path = f"static{temp_video_path}"

        # Ensure temp directory exists
        os.makedirs(os.path.dirname(full_temp_path), exist_ok=True)

        # Save the uploaded file
        with open(full_temp_path, "wb") as f:
            shutil.copyfileobj(video.file, f)

        print(f"💾 Video saved to: {temp_video_path}")

        # Process video with YOLOv5
        print("🔄 Starting video processing...")
        detection_results, predicted_video_path = predict_video(
            temp_video_path, selected_class
        )

        # Add random location data to detections
        updated_results = add_random_location(
            detection_results, GIVEN_LAT, GIVEN_LON, MAX_DISTANCE_KM
        )

        # Send alerts if requested
        if getAlert and updated_results:
            print("📱 Sending alerts...")
            try:
                sendAlert(updated_results)
                print("✅ Alerts sent successfully")
            except Exception as e:
                print(f"⚠️ Warning: Could not send alerts: {e}")

        # Prepare response
        response_data = {
            "status": "success",
            "message": "Video processed successfully",
            "video_data_uri": temp_video_path,
            "predicted_video_url": predicted_video_path,
            "results": updated_results,
            "summary": {
                "total_frames_with_detections": len(updated_results),
                "model_used": selected_class,
                "alerts_sent": getAlert and len(updated_results) > 0,
            },
        }

        print(
            f"✅ Processing complete! Found {len(updated_results)} frames with detections"
        )

        return JSONResponse(content=response_data)

    except HTTPException:
        raise
    except Exception as e:
        print(f"❌ Error processing video: {e}")
        raise HTTPException(status_code=500, detail=f"Error processing video: {str(e)}")


@app.get("/api/models/status")
async def get_models_status():
    """Get the status of loaded models."""
    try:
        from ..core.model_loader import model_loader

        return {
            "models_loaded": model_loader.models_loaded,
            "day_model_available": model_loader.day_model is not None,
            "thermal_model_available": model_loader.thermal_model is not None,
            "day_classes": len(model_loader.day_class_to_animal),
            "thermal_classes": len(model_loader.thermal_class_to_animal),
        }
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error checking model status: {str(e)}"
        )


@app.get("/api/classes/{model_type}")
async def get_model_classes(model_type: str):
    """Get the class mappings for a specific model type."""
    try:
        from ..core.model_loader import model_loader

        if model_type.lower() not in ["daylight", "thermal"]:
            raise HTTPException(
                status_code=400,
                detail="Invalid model type. Use 'daylight' or 'thermal'",
            )

        class_mapping = model_loader.get_class_mapping(model_type)

        return {
            "model_type": model_type,
            "classes": class_mapping,
            "total_classes": len(class_mapping),
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, detail=f"Error getting class mappings: {str(e)}"
        )


def main():
    """Run the FastAPI application."""
    print("🌟 Wildlife Anomaly Detection API")
    print("=" * 50)
    print("📍 Server will be available at: http://127.0.0.1:8000")
    print("📖 API Documentation: http://127.0.0.1:8000/docs")
    print("🔧 Interactive API: http://127.0.0.1:8000/redoc")
    print("=" * 50)

    uvicorn.run(app, host="127.0.0.1", port=8000, log_level="info", access_log=True)


if __name__ == "__main__":
    main()

# Wildlife Anomaly Detection using YOLOv5

A comprehensive wildlife monitoring and anomaly detection system using YOLOv5 deep learning models. This system can detect various wildlife species in both daylight and thermal video feeds, providing real-time alerts and location tracking.

## 🌟 Features

- **Dual Model Support**: Separate models for daylight and thermal/night vision
- **Real-time Detection**: Process video feeds with bounding box annotations
- **Wildlife Species**: Detect 11+ species including elephants, zebras, leopards, etc.
- **Anomaly Alerts**: SMS notifications via <PERSON>wilio for critical detections
- **GPS Integration**: Location tracking with coordinate generation
- **Web Interface**: Complete web-based interface for video upload and monitoring
- **REST API**: Full RESTful API for integration with other systems
- **Production Ready**: Scalable architecture with proper error handling

## 🏗️ Project Structure

```
backend/
├── main.py                 # Main application entry point
├── src/                    # Source code directory
│   ├── __init__.py
│   ├── api/                # FastAPI application
│   │   ├── __init__.py
│   │   └── app.py          # Main FastAPI app with all endpoints
│   ├── core/               # Core YOLOv5 functionality
│   │   ├── __init__.py
│   │   ├── model_loader.py      # YOLOv5 model loading and management
│   │   ├── video_processor.py   # Video processing pipeline
│   │   └── pytorch_compatibility.py  # PyTorch compatibility fixes
│   └── utils/              # Utility modules
│       ├── __init__.py
│       ├── location_generator.py  # GPS coordinate generation
│       ├── load_data.py          # Data loading utilities
│       └── notification.py      # SMS alert system
├── static/                 # Web interface files
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   ├── img/               # Images and assets
│   ├── temp/              # Temporary uploaded videos
│   ├── out/               # Processed output videos
│   └── *.html             # Web pages
├── weights/               # YOLOv5 model weights
│   ├── day-final.pt       # Daylight model
│   └── night-final-aug.pt # Thermal/night model
├── yolov5/               # Original YOLOv5 repository (unmodified)
├── assets/               # Documentation assets
├── pyproject.toml        # Project configuration
├── requirements.txt      # Python dependencies
└── uv.lock              # Dependency lock file
```

## 🚀 Quick Start

### Prerequisites

- Python 3.12+
- UV package manager (recommended) or pip

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Wildlife-Anomaly-Detection-using-YOLOv5l/backend
   ```

2. **Install dependencies**
   ```bash
   # Using UV (recommended)
   uv sync
   
   # Or using pip
   pip install -r requirements.txt
   ```

3. **Run the application**
   ```bash
   # Using UV
   uv run python main.py
   
   # Or using python directly
   python main.py
   ```

4. **Access the application**
   - Web Interface: http://127.0.0.1:8000
   - API Documentation: http://127.0.0.1:8000/docs
   - Interactive API: http://127.0.0.1:8000/redoc

## 📋 API Endpoints

### Core Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/` | Main web interface |
| POST | `/upload/` | Video upload and detection |
| GET | `/health` | Health check |
| GET | `/api/models/status` | Model status information |
| GET | `/api/classes/{model_type}` | Get class mappings |

### Video Detection API

**POST `/upload/`**

Upload a video file for wildlife detection analysis.

**Parameters:**
- `video` (file): Video file (MP4, WebM, etc.)
- `selected_class` (string): Model type - "Daylight" or "Thermal"
- `getAlert` (boolean): Enable SMS alerts for detections

**Response:**
```json
{
  "status": "success",
  "video_data_uri": "/temp/input.mp4",
  "predicted_video_url": "/out/output_video.mp4",
  "results": [
    {
      "frame_number": 1,
      "detections": [
        {
          "class_id": 5,
          "class_name": "Bison",
          "confidence": 0.85,
          "latitude": 22.606803,
          "longitude": 85.338173
        }
      ]
    }
  ],
  "summary": {
    "total_frames_with_detections": 258,
    "model_used": "Daylight",
    "alerts_sent": false
  }
}
```

## 🎯 Detection Classes

### Daylight Model
- Person, Elephant, Zebra, Giraffe, Deer, Bison, Rhino, Boar, Leopard, Vehicle, Fire

### Thermal/Night Model  
- Person, Elephant, Deer, Rhino, Boar, Leopard, Vehicle, Fire

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the root directory:

```env
# Twilio Configuration (for SMS alerts)
TWILIO_ACCOUNT_SID=your_account_sid
TWILIO_AUTH_TOKEN=your_auth_token
TWILIO_PHONE_NUMBER=your_twilio_number

# GPS Configuration
BASE_LATITUDE=22.606803
BASE_LONGITUDE=85.338173
MAX_DISTANCE_KM=20

# Server Configuration
HOST=127.0.0.1
PORT=8000
LOG_LEVEL=info
```

## 🧪 Testing

Run the test suite to verify functionality:

```bash
# Test model loading
uv run python -c "from src.core.model_loader import test_model_loading; test_model_loading()"

# Test video processing
uv run python -c "from src.core.video_processor import test_video_processing; test_video_processing()"
```

## 📊 System Architecture

### Workflow Overview
1. **Input**: Video captured by UAVs, drones, or surveillance cameras
2. **Frame Extraction**: Video broken into individual frames
3. **Detection**: YOLOv5 models identify wildlife and anomalies
4. **Alert System**: SMS notifications sent for critical detections
5. **Visualization**: Results displayed with bounding boxes and coordinates

### Model Architecture
- **Backbone**: CSP-Darknet53 for efficient feature extraction
- **Neck**: Advanced feature pyramid representation
- **Head**: Final object detection outputs with confidence scores

## 🔒 Security Considerations

- Input validation for uploaded files
- File type restrictions for security
- Rate limiting for API endpoints
- Secure credential management
- CORS configuration for frontend integration

## 🚀 Deployment

### Docker Deployment (Recommended)

```dockerfile
FROM python:3.12-slim

WORKDIR /app
COPY . .

RUN pip install uv
RUN uv sync

EXPOSE 8000
CMD ["uv", "run", "python", "main.py"]
```

### Production Considerations

- Use GPU-enabled PyTorch for better performance
- Implement async processing for large videos
- Set up load balancing for multiple instances
- Configure proper logging and monitoring
- Use environment variables for sensitive configuration

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- YOLOv5 team for the excellent object detection framework
- Wildlife conservation organizations for inspiration
- Open source community for tools and libraries

---

**For technical support or questions, please open an issue on GitHub.**

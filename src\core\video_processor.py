"""
Video Processing Module for Wildlife Anomaly Detection
This module handles video processing using YOLOv5 models without modifying YOLOv5 source.
"""

import cv2
import os
from .model_loader import model_loader, plot_bbox


def predict_video(temp_video_path, video_class):
    """
    Process video with YOLOv5 model and return detection results.

    Args:
        temp_video_path (str): Path to the input video file
        video_class (str): Model type - 'Daylight' or 'Thermal'

    Returns:
        tuple: (detection_results, output_video_path)
    """
    print(f"🎬 Starting video prediction for {video_class} model...")

    # Ensure models are loaded
    if not model_loader.models_loaded:
        print("📥 Loading models...")
        if not model_loader.load_models():
            raise Exception("Failed to load YOLOv5 models")

    # Get the appropriate model and class mapping
    if video_class == "Daylight":
        model = model_loader.get_day_model()
        class_dict = model_loader.get_class_mapping("daylight")
        print("☀️ Using daylight model")
    else:
        model = model_loader.get_thermal_model()
        class_dict = model_loader.get_class_mapping("thermal")
        print("🌙 Using thermal model")

    # Open input video
    input_path = f"static/{temp_video_path}"
    cap = cv2.VideoCapture(input_path)

    if not cap.isOpened():
        raise Exception(f"Could not open video file: {input_path}")

    # Get video properties
    fps = int(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))

    print(f"📹 Video info: {width}x{height} @ {fps}fps, {total_frames} frames")

    # Setup output video
    output_path = "/out/output_video.mp4"
    output_full_path = f"static/{output_path}"

    # Ensure output directory exists
    os.makedirs(os.path.dirname(output_full_path), exist_ok=True)

    # Create video writer
    fourcc = cv2.VideoWriter_fourcc(*"mp4v")  # MP4 codec
    out = cv2.VideoWriter(output_full_path, fourcc, fps, (width, height))

    if not out.isOpened():
        raise Exception(f"Could not create output video: {output_full_path}")

    # Process video frame by frame
    results = []
    frame_count = 0

    print("🔄 Processing frames...")

    while cap.isOpened():
        success, frame = cap.read()

        if not success:
            break

        frame_count += 1

        # Resize frame to ensure consistent dimensions
        frame = cv2.resize(frame, (width, height))

        # Run inference
        detections = model(frame)

        # Draw bounding boxes
        annotated_frame = plot_bbox(detections, frame.copy(), class_dict)

        # Write frame to output video
        out.write(annotated_frame)

        # Process detection results
        if len(detections.xyxy[0]) > 0:
            # Get detection data
            classes = detections.pandas().xyxy[0]["class"]
            confidences = detections.pandas().xyxy[0]["confidence"]

            frame_detections = {"frame_number": frame_count, "detections": []}

            for class_id, confidence in zip(classes, confidences):
                detection_obj = {
                    "class_id": int(class_id),
                    "class_name": class_dict.get(int(class_id), "Unknown"),
                    "confidence": float(confidence),
                }
                frame_detections["detections"].append(detection_obj)

            results.append(frame_detections)

        # Progress indicator
        if frame_count % 30 == 0:  # Every 30 frames
            progress = (frame_count / total_frames) * 100 if total_frames > 0 else 0
            print(f"📊 Progress: {progress:.1f}% ({frame_count}/{total_frames} frames)")

    # Cleanup
    cap.release()
    out.release()

    print(f"✅ Video processing complete!")
    print(f"📁 Output saved to: {output_path}")
    print(f"🔍 Total detections: {len(results)} frames with objects")

    return results, output_path


def test_video_processing():
    """Test video processing with a sample video."""
    print("🧪 Testing video processing...")

    # Check for test video
    test_videos = [
        "static/temp/bison.mp4",
        "static/temp/cut.mp4",
        "static/temp/zebra.mp4",
    ]

    test_video = None
    for video in test_videos:
        if os.path.exists(video):
            test_video = video
            break

    if not test_video:
        print("❌ No test video found")
        return False

    try:
        # Extract relative path
        relative_path = test_video.replace("static/", "")

        print(f"🎬 Testing with video: {relative_path}")
        results, output_path = predict_video(relative_path, "Daylight")

        print(f"✅ Video processing test passed!")
        print(f"📊 Results: {len(results)} frames with detections")
        print(f"📁 Output: {output_path}")

        return True

    except Exception as e:
        print(f"❌ Video processing test failed: {e}")
        return False


if __name__ == "__main__":
    # Test video processing when run directly
    test_video_processing()

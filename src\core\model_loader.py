"""
YOLOv5 Model Loader with Compatibility Layer
This module loads YOLOv5 models without modifying the original YOLOv5 code.
"""

import torch
import cv2
import pathlib
import os
import sys
import warnings

# Import our compatibility layer first
from . import pytorch_compatibility

# Suppress warnings for cleaner output
warnings.filterwarnings("ignore", category=UserWarning)

# Windows path compatibility for YOLOv5
temp = pathlib.PosixPath
pathlib.PosixPath = pathlib.WindowsPath


class YOLOv5ModelLoader:
    """
    A wrapper class for loading and managing YOLOv5 models with compatibility fixes.
    """

    def __init__(self):
        self.day_model = None
        self.thermal_model = None
        self.models_loaded = False

        # Class mappings
        self.day_class_to_animal = {
            0: "Person",
            1: "Elephant",
            2: "Zebra",
            3: "Giraffe",
            4: "Deer",
            5: "Bison",
            6: "Rhino",
            7: "Boar",
            8: "Leopard",
            9: "Vehicle",
            10: "Fire",
        }

        self.thermal_class_to_animal = {
            0: "Person",
            1: "Elephant",
            2: "Deer",
            3: "Rhino",
            4: "Boar",
            5: "Leopard",
            6: "Vehicle",
            7: "Fire",
        }

    def load_models(self):
        """
        Load both day and thermal models with compatibility fixes.
        """
        if self.models_loaded:
            print("✓ Models already loaded")
            return True

        try:
            print("🔄 Loading YOLOv5 models...")

            # Verify weight files exist
            day_weights = "weights/day-final.pt"
            thermal_weights = "weights/night-final-aug.pt"

            if not os.path.exists(day_weights):
                raise FileNotFoundError(f"Day model weights not found: {day_weights}")
            if not os.path.exists(thermal_weights):
                raise FileNotFoundError(
                    f"Thermal model weights not found: {thermal_weights}"
                )

            # Load day model
            print("📅 Loading day model...")
            self.day_model = torch.hub.load(
                "yolov5",
                "custom",
                path=day_weights,
                source="local",
                force_reload=True,
                trust_repo=True,
                device="cpu",
            )
            print("✓ Day model loaded successfully")

            # Load thermal model
            print("🌙 Loading thermal model...")
            self.thermal_model = torch.hub.load(
                "yolov5",
                "custom",
                path=thermal_weights,
                source="local",
                force_reload=True,
                trust_repo=True,
                device="cpu",
            )
            print("✓ Thermal model loaded successfully")

            self.models_loaded = True
            print("🎉 All models loaded successfully!")
            return True

        except Exception as e:
            print(f"❌ Error loading models: {e}")
            return False

    def get_day_model(self):
        """Get the day model, loading if necessary."""
        if not self.models_loaded:
            self.load_models()
        return self.day_model

    def get_thermal_model(self):
        """Get the thermal model, loading if necessary."""
        if not self.models_loaded:
            self.load_models()
        return self.thermal_model

    def get_class_mapping(self, model_type):
        """Get class mapping for the specified model type."""
        if model_type.lower() == "daylight":
            return self.day_class_to_animal
        else:
            return self.thermal_class_to_animal


# Create a global instance
model_loader = YOLOv5ModelLoader()


def get_color(class_id):
    """Get color for bounding box visualization."""
    colors = [
        (255, 99, 71),  # Tomato
        (124, 252, 0),  # Lawn Green
        (255, 215, 0),  # Gold
        (255, 255, 0),  # Yellow
        (0, 255, 255),  # Cyan
        (255, 0, 255),  # Magenta
        (255, 218, 185),  # Peach Puff
        (138, 43, 226),  # Blue Violet
        (255, 20, 147),  # Deep Pink
        (176, 196, 222),  # Light Steel Blue
        (0, 250, 154),  # Medium Spring Green
    ]
    return colors[class_id % len(colors)]


def plot_bbox(results, frame, class_dict):
    """Plot bounding boxes on the frame."""
    for box in results.xyxy[0]:
        xA, yA, xB, yB, confidence, class_id = box
        class_id = int(class_id)
        class_name = class_dict.get(class_id, "Unknown")

        # Get color for this class
        color = get_color(class_id)

        # Convert coordinates to integers
        xA, yA, xB, yB = int(xA), int(yA), int(xB), int(yB)

        # Draw bounding box
        cv2.rectangle(frame, (xA, yA), (xB, yB), color, 2)

        # Add label
        label = f"{class_name}: {confidence:.2f}"
        y = yA - 15 if yA - 15 > 15 else yA + 15
        cv2.putText(frame, label, (xA, y), cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)

    return frame


# Test function to verify models load correctly
def test_model_loading():
    """Test function to verify models can be loaded."""
    print("🧪 Testing model loading...")
    success = model_loader.load_models()
    if success:
        print("✅ Model loading test passed!")
        return True
    else:
        print("❌ Model loading test failed!")
        return False


if __name__ == "__main__":
    # Test the model loading when run directly
    test_model_loading()

#!/usr/bin/env python3
"""
Wildlife Anomaly Detection API - Main Entry Point
YOLOv5-based wildlife detection and monitoring system
"""

import sys
import os
import uvicorn

# Add the src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def main():
    """Main entry point for the Wildlife Anomaly Detection API."""
    print("🌟 Wildlife Anomaly Detection API")
    print("=" * 50)
    print("📍 Server will be available at: http://127.0.0.1:8000")
    print("📖 API Documentation: http://127.0.0.1:8000/docs")
    print("🔧 Interactive API: http://127.0.0.1:8000/redoc")
    print("=" * 50)
    
    # Import the FastAPI app
    from src.api.app import app
    
    # Run the server
    uvicorn.run(
        app, 
        host="127.0.0.1", 
        port=8000, 
        log_level="info",
        access_log=True
    )

if __name__ == "__main__":
    main()

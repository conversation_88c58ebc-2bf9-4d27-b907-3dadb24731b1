"""
PyTorch Compatibility Layer for YOLOv5
This module provides compatibility fixes without modifying the original YOLOv5 code.
"""

import torch
import functools
import warnings
import pathlib

# Store the original torch.load function
_original_torch_load = torch.load


def patched_torch_load(*args, **kwargs):
    """
    Patched version of torch.load that automatically sets weights_only=False
    for YOLOv5 model loading compatibility with PyTorch 2.6+
    """
    # If weights_only is not specified and we're loading a .pt file, set it to False
    if "weights_only" not in kwargs:
        # Check if we're loading a .pt file (common for YOLOv5 models)
        if args and isinstance(args[0], (str, pathlib.Path)):
            file_path = str(args[0])
            if file_path.endswith(".pt"):
                kwargs["weights_only"] = False
                warnings.filterwarnings("ignore", message=".*weights_only.*")

    return _original_torch_load(*args, **kwargs)


def apply_pytorch_compatibility():
    """
    Apply PyTorch compatibility patches for YOLOv5 without modifying YOLOv5 source code.
    This function monkey-patches torch.load to handle the weights_only parameter.
    """
    # Monkey patch torch.load
    torch.load = patched_torch_load
    print("✓ Applied PyTorch compatibility patch for YOLOv5")


def restore_pytorch_original():
    """
    Restore the original torch.load function.
    """
    torch.load = _original_torch_load
    print("✓ Restored original PyTorch functions")


# Automatically apply compatibility when this module is imported
apply_pytorch_compatibility()
